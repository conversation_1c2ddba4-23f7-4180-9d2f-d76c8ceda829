import * as XLSX from 'xlsx';
import { EXCEL_HEADERS, SAMPLE_NEW_APPLICATION, SAMPLE_WORK_STATUS } from './constants.js';

// Export data to Excel
export const exportToExcel = (data, type, filename) => {
  let headers, mappedData;
  
  if (type === 'applications') {
    headers = EXCEL_HEADERS.NEW_APPLICATIONS;
    mappedData = data.map(app => [
      app.srNo || '',
      app.rd || '',
      app.minor || '',
      app.beneficiaryVillage || '',
      app.status || '',
      app.extensionRemodelingKachcha || '',
      app.farmerName || '',
      app.contactNumber || '',
      app.reference || '',
      app.atr || ''
    ]);
  } else if (type === 'works') {
    headers = EXCEL_HEADERS.WORK_STATUS;
    mappedData = data.map(work => [
      work.waterCourseName || '',
      work.nameOfWork || '',
      work.aaNoDate || '',
      work.contractorName || '',
      work.stipulatedDateOfCompletion || '',
      work.currentPhysicalStatus || 0,
      work.currentFinancialStatus || 0,
      work.remarks || ''
    ]);
  }
  
  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...mappedData]);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');
  
  // Auto-size columns
  const colWidths = headers.map((header, i) => {
    const maxLength = Math.max(
      header.length,
      ...mappedData.map(row => String(row[i] || '').length)
    );
    return { wch: Math.min(maxLength + 2, 50) };
  });
  worksheet['!cols'] = colWidths;
  
  XLSX.writeFile(workbook, filename);
};

// Generate sample Excel file
export const generateSampleExcel = (type) => {
  let headers, sampleData, filename;
  
  if (type === 'applications') {
    headers = EXCEL_HEADERS.NEW_APPLICATIONS;
    sampleData = [
      [1, 'RD-1', 'Sample Minor 1', 'Sample Village 1', 'Pending', 'Extension', 'John Doe', '9876543210', 'REF-001', ''],
      [2, 'RD-2', 'Sample Minor 2', 'Sample Village 2', 'In Progress', 'Remodeling', 'Jane Smith', '9876543211', 'REF-002', 'Under review'],
      [3, 'RD-3', 'Sample Minor 3', 'Sample Village 3', 'Completed', 'Kachcha khal', 'Bob Johnson', '9876543212', 'REF-003', 'Approved and completed']
    ];
    filename = 'sample_new_applications.xlsx';
  } else if (type === 'works') {
    headers = EXCEL_HEADERS.WORK_STATUS;
    sampleData = [
      ['Sample Minor 1', 'Construction of Lined Water Course', 'AA/2024/001 dt. 15-01-2024', 'ABC Construction', '2024-06-15', 75, 850000, 'Work in progress'],
      ['Sample Minor 2', 'Renovation of Water Course', 'AA/2024/002 dt. 20-01-2024', 'XYZ Contractors', '2024-07-20', 45, 650000, 'Delayed due to weather'],
      ['Sample Minor 3', 'Extension of Water Course', 'AA/2024/003 dt. 10-01-2024', 'PQR Infrastructure', '2024-05-30', 100, 1200000, 'Completed successfully']
    ];
    filename = 'sample_work_status.xlsx';
  }
  
  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sample Data');
  
  // Auto-size columns
  const colWidths = headers.map((header, i) => {
    const maxLength = Math.max(
      header.length,
      ...sampleData.map(row => String(row[i] || '').length)
    );
    return { wch: Math.min(maxLength + 2, 50) };
  });
  worksheet['!cols'] = colWidths;
  
  XLSX.writeFile(workbook, filename);
};

// Parse Excel file
export const parseExcelFile = (file, type) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length < 2) {
          reject(new Error('Excel file must contain at least a header row and one data row'));
          return;
        }
        
        const headers = jsonData[0];
        const rows = jsonData.slice(1);
        
        let expectedHeaders, parsedData;
        
        if (type === 'applications') {
          expectedHeaders = EXCEL_HEADERS.NEW_APPLICATIONS;
          parsedData = rows.map((row, index) => ({
            srNo: row[0] || '',
            rd: row[1] || '',
            minor: row[2] || '', // Required field
            beneficiaryVillage: row[3] || '',
            status: row[4] || 'Pending',
            extensionRemodelingKachcha: row[5] || '',
            farmerName: row[6] || '',
            contactNumber: row[7] || '',
            reference: row[8] || '',
            atr: row[9] || ''
          })).filter(item => item.minor); // Filter out rows without minor (required field)
          
        } else if (type === 'works') {
          expectedHeaders = EXCEL_HEADERS.WORK_STATUS;
          parsedData = rows.map((row, index) => ({
            waterCourseName: row[0] || '',
            nameOfWork: row[1] || '',
            aaNoDate: row[2] || '',
            contractorName: row[3] || '',
            stipulatedDateOfCompletion: row[4] || '',
            currentPhysicalStatus: parseFloat(row[5]) || 0,
            currentFinancialStatus: parseFloat(row[6]) || 0,
            remarks: row[7] || ''
          })).filter(item => item.waterCourseName); // Filter out rows without water course name
        }
        
        resolve({
          headers,
          data: parsedData,
          totalRows: rows.length,
          validRows: parsedData.length
        });
        
      } catch (error) {
        reject(new Error(`Error parsing Excel file: ${error.message}`));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsArrayBuffer(file);
  });
};

// Validate Excel data
export const validateExcelData = (data, type) => {
  const errors = [];
  
  data.forEach((item, index) => {
    const rowNum = index + 2; // +2 because Excel rows start at 1 and we skip header
    
    if (type === 'applications') {
      if (!item.minor || item.minor.trim() === '') {
        errors.push(`Row ${rowNum}: MINOR (water course name) is required`);
      }
      
      if (item.contactNumber && !/^\d{10}$/.test(item.contactNumber.replace(/\D/g, ''))) {
        errors.push(`Row ${rowNum}: Contact number should be 10 digits`);
      }
      
    } else if (type === 'works') {
      if (!item.waterCourseName || item.waterCourseName.trim() === '') {
        errors.push(`Row ${rowNum}: Water Course Name is required`);
      }
      
      if (item.currentPhysicalStatus < 0 || item.currentPhysicalStatus > 100) {
        errors.push(`Row ${rowNum}: Physical status should be between 0 and 100`);
      }
      
      if (item.currentFinancialStatus < 0) {
        errors.push(`Row ${rowNum}: Financial status should be a positive number`);
      }
    }
  });
  
  return errors;
};

// Format data for display
export const formatDataForDisplay = (data, type) => {
  if (type === 'applications') {
    return data.map(item => ({
      ...item,
      contactNumber: item.contactNumber ? String(item.contactNumber) : '',
      srNo: item.srNo ? String(item.srNo) : ''
    }));
  } else if (type === 'works') {
    return data.map(item => ({
      ...item,
      currentPhysicalStatus: Number(item.currentPhysicalStatus) || 0,
      currentFinancialStatus: Number(item.currentFinancialStatus) || 0
    }));
  }
  
  return data;
};
