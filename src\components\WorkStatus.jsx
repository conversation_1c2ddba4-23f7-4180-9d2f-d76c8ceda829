import React, { useState, useEffect } from 'react';
import {
  Plus,
  Download,
  Upload,
  Edit,
  Trash2,
  Search,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Briefcase
} from 'lucide-react';
import {
  getWorkStatus,
  addWorkStatus,
  updateWorkStatus,
  deleteWorkStatus
} from '../utils/dataManager.js';
import { exportToExcel, generateSampleExcel, parseExcelFile, validateExcelData } from '../utils/excelUtils.js';
import { SAMPLE_WORK_STATUS } from '../utils/constants.js';
import { getCurrentUser } from '../utils/auth.js';

const WorkStatus = () => {
  const [works, setWorks] = useState([]);
  const [filteredWorks, setFilteredWorks] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingWork, setEditingWork] = useState(null);
  const [formData, setFormData] = useState(SAMPLE_WORK_STATUS);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const user = getCurrentUser();

  useEffect(() => {
    loadWorks();
  }, []);

  useEffect(() => {
    filterWorks();
  }, [works, searchQuery]);

  const loadWorks = () => {
    setLoading(true);
    try {
      const workData = getWorkStatus();
      setWorks(workData);
    } catch (error) {
      showMessage('error', 'Error loading work status: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const filterWorks = () => {
    let filtered = works;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(work =>
        work.waterCourseName?.toLowerCase().includes(query) ||
        work.nameOfWork?.toLowerCase().includes(query) ||
        work.contractorName?.toLowerCase().includes(query) ||
        work.aaNoDate?.toLowerCase().includes(query)
      );
    }

    setFilteredWorks(filtered);
  };

  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.waterCourseName.trim()) {
      showMessage('error', 'Water Course Name is required');
      return;
    }

    try {
      if (editingWork) {
        await updateWorkStatus(editingWork.id, formData);
        showMessage('success', 'Work status updated successfully');
      } else {
        await addWorkStatus(formData);
        showMessage('success', 'Work status added successfully');
      }

      setShowForm(false);
      setEditingWork(null);
      setFormData(SAMPLE_WORK_STATUS);
      loadWorks();
    } catch (error) {
      showMessage('error', error.message);
    }
  };

  const handleEdit = (work) => {
    setEditingWork(work);
    setFormData({ ...work });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this work status?')) {
      try {
        await deleteWorkStatus(id);
        showMessage('success', 'Work status deleted successfully');
        loadWorks();
      } catch (error) {
        showMessage('error', error.message);
      }
    }
  };

  const handleExport = () => {
    const dataToExport = filteredWorks.length > 0 ? filteredWorks : works;
    exportToExcel(dataToExport, 'works', 'work_status.xlsx');
    showMessage('success', 'Data exported successfully');
  };

  const handleSampleDownload = () => {
    generateSampleExcel('works');
    showMessage('success', 'Sample file downloaded');
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      const result = await parseExcelFile(file, 'works');
      const errors = validateExcelData(result.data, 'works');

      if (errors.length > 0) {
        showMessage('error', `Validation errors: ${errors.join(', ')}`);
        return;
      }

      // Add all valid work status entries
      for (const workData of result.data) {
        await addWorkStatus(workData);
      }

      showMessage('success', `Successfully imported ${result.validRows} work status entries`);
      loadWorks();
    } catch (error) {
      showMessage('error', 'Error importing file: ' + error.message);
    } finally {
      setUploading(false);
      e.target.value = '';
    }
  };

  const getProgressColor = (progress) => {
    if (progress >= 100) return 'text-green-600 bg-green-100';
    if (progress >= 75) return 'text-blue-600 bg-blue-100';
    if (progress >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Work Status</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track progress of ongoing works with Administrative Approval (AA)
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowForm(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Work Status
          </button>
        </div>
      </div>

      {/* Message */}
      {message.text && (
        <div className={`p-4 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
            'bg-red-50 text-red-800 border border-red-200'
          }`}>
          <div className="flex">
            {message.type === 'success' ?
              <CheckCircle className="h-5 w-5 mr-2" /> :
              <AlertCircle className="h-5 w-5 mr-2" />
            }
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {/* Filters and Actions */}
      <div className="card p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search works..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field pl-10 w-full sm:w-64"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <button
              onClick={handleSampleDownload}
              className="btn-secondary"
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Sample Excel
            </button>
            <label className="btn-secondary cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              {uploading ? 'Uploading...' : 'Import Excel'}
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileUpload}
                className="hidden"
                disabled={uploading}
              />
            </label>
            <button
              onClick={handleExport}
              className="btn-secondary"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </button>
          </div>
        </div>
      </div>

      {/* Works Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="table-header">
              <tr>
                <th className="px-6 py-3 text-left">Water Course</th>
                <th className="px-6 py-3 text-left">Work Name</th>
                <th className="px-6 py-3 text-left">AA No. & Date</th>
                <th className="px-6 py-3 text-left">Contractor</th>
                <th className="px-6 py-3 text-left">Completion Date</th>
                <th className="px-6 py-3 text-left">Physical Progress</th>
                <th className="px-6 py-3 text-left">Financial Status</th>
                <th className="px-6 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredWorks.map((work) => (
                <tr key={work.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {work.waterCourseName}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                    {work.nameOfWork}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {work.aaNoDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {work.contractorName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {work.stipulatedDateOfCompletion}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProgressColor(work.currentPhysicalStatus)}`}>
                      {work.currentPhysicalStatus}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ₹{work.currentFinancialStatus?.toLocaleString('en-IN')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(work)}
                        className="text-primary-600 hover:text-primary-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(work.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredWorks.length === 0 && (
            <div className="text-center py-12">
              <Briefcase className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No work status found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchQuery ? 'Try adjusting your search' : 'Get started by adding a new work status'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingWork ? 'Edit Work Status' : 'Add New Work Status'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Water Course Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.waterCourseName}
                      onChange={(e) => setFormData({ ...formData, waterCourseName: e.target.value })}
                      className="input-field"
                      placeholder="Enter water course name"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Name of Work</label>
                    <input
                      type="text"
                      value={formData.nameOfWork}
                      onChange={(e) => setFormData({ ...formData, nameOfWork: e.target.value })}
                      className="input-field"
                      placeholder="Enter work description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">AA No. & Date</label>
                    <input
                      type="text"
                      value={formData.aaNoDate}
                      onChange={(e) => setFormData({ ...formData, aaNoDate: e.target.value })}
                      className="input-field"
                      placeholder="e.g., AA/2024/001 dt. 15-01-2024"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Contractor Name</label>
                    <input
                      type="text"
                      value={formData.contractorName}
                      onChange={(e) => setFormData({ ...formData, contractorName: e.target.value })}
                      className="input-field"
                      placeholder="Enter contractor name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stipulated Date of Completion</label>
                    <input
                      type="date"
                      value={formData.stipulatedDateOfCompletion}
                      onChange={(e) => setFormData({ ...formData, stipulatedDateOfCompletion: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Current Physical Status (%)</label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.currentPhysicalStatus}
                      onChange={(e) => setFormData({ ...formData, currentPhysicalStatus: parseFloat(e.target.value) || 0 })}
                      className="input-field"
                      placeholder="0-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Current Financial Status (₹)</label>
                    <input
                      type="number"
                      min="0"
                      value={formData.currentFinancialStatus}
                      onChange={(e) => setFormData({ ...formData, currentFinancialStatus: parseFloat(e.target.value) || 0 })}
                      className="input-field"
                      placeholder="Enter amount in rupees"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Remarks</label>
                    <textarea
                      value={formData.remarks}
                      onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
                      className="input-field"
                      rows="3"
                      placeholder="Add any remarks or notes..."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingWork(null);
                      setFormData(SAMPLE_WORK_STATUS);
                    }}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    {editingWork ? 'Update' : 'Add'} Work Status
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkStatus;
