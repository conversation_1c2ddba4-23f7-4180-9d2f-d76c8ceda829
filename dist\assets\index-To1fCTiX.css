@tailwind base;@tailwind components;@tailwind utilities;@layer base{body{@apply font-sans antialiased bg-gray-50;}}@layer components{.btn-primary{@apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;}.btn-secondary{@apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;}.card{@apply bg-white rounded-lg shadow-md border border-gray-200;}.input-field{@apply block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500;}.table-header{@apply bg-gray-50 text-gray-700 font-medium text-sm uppercase tracking-wider;}.status-pending{@apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;}.status-completed{@apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;}.status-in-progress{@apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;}}
