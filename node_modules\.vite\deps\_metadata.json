{"hash": "a0c1a7f4", "configHash": "cb6fedec", "lockfileHash": "4c1efad6", "browserHash": "66aa2482", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1c79d71a", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "15060f2e", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "6ad99c46", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "470f85c1", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "595bb55f", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9a609146", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "9dc00a11", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "5fe821fe", "needsInterop": false}}, "chunks": {"chunk-FYDILROA": {"file": "chunk-FYDILROA.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}