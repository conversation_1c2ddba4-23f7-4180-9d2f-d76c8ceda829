{"hash": "13620e6f", "configHash": "cb6fedec", "lockfileHash": "f8f15581", "browserHash": "bfe5282a", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9b38fa95", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a013effe", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1dcab165", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ae26c824", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "20d6b0ba", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "49410935", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "7e468f33", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "ee5982ef", "needsInterop": false}}, "chunks": {"chunk-FYDILROA": {"file": "chunk-FYDILROA.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}