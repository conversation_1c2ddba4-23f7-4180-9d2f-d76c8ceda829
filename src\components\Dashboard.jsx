import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Briefcase, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  Users,
  Search
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { getDashboardStats, searchData, getSubdivisions } from '../utils/dataManager.js';
import { getCurrentUser } from '../utils/auth.js';

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [selectedSubdivision, setSelectedSubdivision] = useState('');
  const [loading, setLoading] = useState(true);
  const user = getCurrentUser();
  const subdivisions = getSubdivisions();

  useEffect(() => {
    loadDashboardData();
  }, [selectedSubdivision]);

  const loadDashboardData = () => {
    setLoading(true);
    try {
      const dashboardStats = getDashboardStats();
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      const results = searchData(searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults(null);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const applicationChartData = [
    { name: 'Pending', value: stats?.pendingApplications || 0, color: '#f59e0b' },
    { name: 'In Progress', value: stats?.inProgressApplications || 0, color: '#3b82f6' },
    { name: 'Completed', value: stats?.completedApplications || 0, color: '#10b981' }
  ];

  const workProgressData = [
    { name: 'Ongoing', value: stats?.ongoingWorks || 0 },
    { name: 'Completed', value: stats?.completedWorks || 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back, {user.name} - {user.designation}
          </p>
        </div>
        
        {user.role === 'xen' && (
          <div className="mt-4 sm:mt-0">
            <select
              value={selectedSubdivision}
              onChange={(e) => setSelectedSubdivision(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="">All Subdivisions</option>
              {subdivisions.map((sub) => (
                <option key={sub.id} value={sub.name}>
                  {sub.shortName}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Search Bar */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by water course name, farmer name, village, or status..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="input-field pl-10"
              />
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="btn-primary"
          >
            Search
          </button>
        </div>

        {searchResults && (
          <div className="mt-4 border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Search Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">
                  New Applications ({searchResults.applications.length})
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {searchResults.applications.map((app) => (
                    <div key={app.id} className="p-2 bg-gray-50 rounded text-sm">
                      <p className="font-medium">{app.minor}</p>
                      <p className="text-gray-600">{app.farmerName} - {app.beneficiaryVillage}</p>
                      <span className={`status-${app.status.toLowerCase().replace(' ', '-')}`}>
                        {app.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-700 mb-2">
                  Work Status ({searchResults.works.length})
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {searchResults.works.map((work) => (
                    <div key={work.id} className="p-2 bg-gray-50 rounded text-sm">
                      <p className="font-medium">{work.waterCourseName}</p>
                      <p className="text-gray-600">{work.nameOfWork}</p>
                      <p className="text-gray-600">{work.currentPhysicalStatus}% Complete</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Applications</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalApplications || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Applications</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.pendingApplications || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Briefcase className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Works</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalWorks || 0}</p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Progress</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.averageProgress || 0}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Application Status Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={applicationChartData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}`}
              >
                {applicationChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Work Progress Overview</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={workProgressData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
