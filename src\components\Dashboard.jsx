import React, { useState, useEffect } from 'react';
import {
  FileText,
  Briefcase,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Users,
  Search
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { getDashboardStats, searchData, getSubdivisions } from '../utils/dataManager.js';
import { getCurrentUser } from '../utils/auth.js';

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [selectedSubdivision, setSelectedSubdivision] = useState('');
  const [loading, setLoading] = useState(true);
  const user = getCurrentUser();
  const subdivisions = getSubdivisions();

  useEffect(() => {
    loadDashboardData();
  }, [selectedSubdivision]);

  const loadDashboardData = () => {
    setLoading(true);
    try {
      const dashboardStats = getDashboardStats();
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      const results = searchData(searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults(null);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const applicationChartData = [
    { name: 'Pending', value: stats?.pendingApplications || 0, color: '#f59e0b' },
    { name: 'In Progress', value: stats?.inProgressApplications || 0, color: '#3b82f6' },
    { name: 'Completed', value: stats?.completedApplications || 0, color: '#10b981' }
  ];

  const workProgressData = [
    { name: 'Ongoing', value: stats?.ongoingWorks || 0 },
    { name: 'Completed', value: stats?.completedWorks || 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Welcome back, <span className="font-semibold text-gray-900">{user.name}</span> - {user.designation}
          </p>
        </div>

        {user.role === 'xen' && (
          <div className="mt-6 sm:mt-0">
            <select
              value={selectedSubdivision}
              onChange={(e) => setSelectedSubdivision(e.target.value)}
              className="input-field w-full sm:w-auto min-w-[200px]"
            >
              <option value="">All Subdivisions</option>
              {subdivisions.map((sub) => (
                <option key={sub.id} value={sub.name}>
                  {sub.shortName}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Search Bar */}
      <div className="card p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search by water course name, farmer name, village, or status..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="search-input"
              />
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="btn-primary"
          >
            <Search className="h-4 w-4 mr-2" />
            Search
          </button>
        </div>

        {searchResults && (
          <div className="mt-4 border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Search Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">
                  New Applications ({searchResults.applications.length})
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {searchResults.applications.map((app) => (
                    <div key={app.id} className="p-2 bg-gray-50 rounded text-sm">
                      <p className="font-medium">{app.minor}</p>
                      <p className="text-gray-600">{app.farmerName} - {app.beneficiaryVillage}</p>
                      <span className={`status-${app.status.toLowerCase().replace(' ', '-')}`}>
                        {app.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-700 mb-2">
                  Work Status ({searchResults.works.length})
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {searchResults.works.map((work) => (
                    <div key={work.id} className="p-2 bg-gray-50 rounded text-sm">
                      <p className="font-medium">{work.waterCourseName}</p>
                      <p className="text-gray-600">{work.nameOfWork}</p>
                      <p className="text-gray-600">{work.currentPhysicalStatus}% Complete</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="stat-card group hover:scale-105 transition-transform duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Applications</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalApplications || 0}</p>
            </div>
            <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow">
              <FileText className="h-8 w-8 text-white" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600">
            <TrendingUp className="h-4 w-4 mr-1" />
            <span className="font-medium">Active tracking</span>
          </div>
        </div>

        <div className="stat-card group hover:scale-105 transition-transform duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Pending Applications</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.pendingApplications || 0}</p>
            </div>
            <div className="p-4 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow">
              <Clock className="h-8 w-8 text-white" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-amber-600">
            <AlertCircle className="h-4 w-4 mr-1" />
            <span className="font-medium">Needs attention</span>
          </div>
        </div>

        <div className="stat-card group hover:scale-105 transition-transform duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Total Works</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.totalWorks || 0}</p>
            </div>
            <div className="p-4 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow">
              <Briefcase className="h-8 w-8 text-white" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600">
            <CheckCircle className="h-4 w-4 mr-1" />
            <span className="font-medium">In progress</span>
          </div>
        </div>

        <div className="stat-card group hover:scale-105 transition-transform duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Avg. Progress</p>
              <p className="text-3xl font-bold text-gray-900">{stats?.averageProgress || 0}%</p>
            </div>
            <div className="p-4 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow">
              <TrendingUp className="h-8 w-8 text-white" />
            </div>
          </div>
          <div className="mt-4">
            <div className="progress-bar">
              <div
                className="progress-fill bg-gradient-to-r from-purple-500 to-indigo-600"
                style={{ width: `${stats?.averageProgress || 0}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card p-8">
          <div className="card-header">
            <h3 className="text-xl font-semibold text-white">Application Status Distribution</h3>
            <p className="text-blue-100 text-sm mt-1">Overview of all application statuses</p>
          </div>
          <div className="p-6">
            <ResponsiveContainer width="100%" height={350}>
              <PieChart>
                <Pie
                  data={applicationChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                  labelLine={false}
                >
                  {applicationChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="card p-8">
          <div className="card-header">
            <h3 className="text-xl font-semibold text-white">Work Progress Overview</h3>
            <p className="text-blue-100 text-sm mt-1">Comparison of ongoing vs completed works</p>
          </div>
          <div className="p-6">
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={workProgressData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  tick={{ fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <YAxis
                  tick={{ fill: '#6b7280' }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar
                  dataKey="value"
                  fill="url(#colorGradient)"
                  radius={[8, 8, 0, 0]}
                />
                <defs>
                  <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.9} />
                    <stop offset="95%" stopColor="#1d4ed8" stopOpacity={0.9} />
                  </linearGradient>
                </defs>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
