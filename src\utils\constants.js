export const SUBDIVISIONS = [
  'CAD Subdivision 1, Hisar',
  'CAD Subdivision 2, Hisar',
  'Barwala CAD Subdivision',
  'MICAD Sub Division, Narwana'
];

export const APPLICATION_STATUS = [
  'Pending',
  'In Progress',
  'Completed',
  'Rejected'
];

export const WORK_TYPES = [
  'Extension',
  'Remodeling',
  'Kachcha khal'
];

export const USER_ROLES = {
  XEN: 'xen',
  SUBDIVISION: 'subdivision'
};

export const LOCAL_STORAGE_KEYS = {
  USER: 'micad_user',
  TOKEN: 'micad_token'
};

export const SAMPLE_NEW_APPLICATION = {
  srNo: '',
  rd: '',
  minor: '',
  beneficiaryVillage: '',
  status: 'Pending',
  extensionRemodelingKachcha: '',
  farmerName: '',
  contactNumber: '',
  reference: '',
  atr: ''
};

export const SAMPLE_WORK_STATUS = {
  waterCourseName: '',
  nameOfWork: '',
  aaNoDate: '',
  contractorName: '',
  stipulatedDateOfCompletion: '',
  currentPhysicalStatus: 0,
  currentFinancialStatus: 0,
  remarks: ''
};

export const EXCEL_HEADERS = {
  NEW_APPLICATIONS: [
    'Sr. No',
    'RD',
    'MINOR',
    'BENEFICIARY Village',
    'STATUS',
    'Extension/Remodeling/Kachcha khal',
    'Farmer\'s Name',
    'Contact Number',
    'Reference',
    'ATR'
  ],
  WORK_STATUS: [
    'Water Course Name',
    'Name of Work',
    'AA No. & Date',
    'Contractor Name',
    'Stipulated Date of Completion',
    'Current Physical Status (%)',
    'Current Financial Status (Expenditure)',
    'Remarks'
  ]
};
