# MICAD Division Hisar - Help Guide

## System Overview
This is the Project Monitoring & Status Reporting System for MICAD Division Hisar, designed to track new farmer applications and work progress status.

## User Roles

### Executive Engineer (Xen)
- **Access**: Full system access across all subdivisions
- **Capabilities**:
  - View dashboard with comprehensive statistics
  - Manage all new applications and work status
  - Create, edit, and delete user accounts
  - Export data from all subdivisions
  - Search across all data

### Subdivision Users (SDO/JE/Clerk)
- **Access**: Limited to their specific subdivision
- **Capabilities**:
  - View and manage applications for their subdivision only
  - Add, edit, and delete applications and work status
  - Bulk upload via Excel files
  - Export their subdivision's data
  - Add ATR (Action Taken Report) remarks

## Features

### New Applications Management
- **Required Field**: MINOR (water course name) is the only mandatory field
- **Data Fields**: Sr. No, RD, MINOR, Beneficiary Village, Status, Extension/Remodeling/Kachcha khal, Farmer's Name, Contact Number, Reference, ATR
- **Status Options**: Pending, In Progress, Completed, Rejected
- **Excel Import/Export**: Bulk operations supported

### Work Status Management
- **Data Fields**: Water Course Name, Name of Work, AA No. & Date, Contractor Name, Stipulated Date of Completion, Current Physical Status (%), Current Financial Status (₹), Remarks
- **Progress Tracking**: Visual indicators for completion percentage
- **Excel Import/Export**: Bulk operations supported

### Excel Operations
1. **Sample Download**: Download template files with sample data
2. **Bulk Import**: Upload Excel files to add multiple records
3. **Data Export**: Download current data in Excel format

### Search & Filter
- **Global Search**: Search across water course names, farmer names, villages, and status
- **Status Filtering**: Filter applications by status
- **Real-time Results**: Instant search results as you type

## Getting Started

### For Subdivision Users
1. Log in with your assigned credentials
2. Navigate to "New Applications" or "Work Status"
3. Use "Add" button for single entries or "Import Excel" for bulk uploads
4. Download sample Excel files to understand the format
5. Add ATR remarks to applications as needed

### For Executive Engineer
1. Access the dashboard for overview statistics
2. Use subdivision filter to view specific subdivision data
3. Manage user accounts in "User Management"
4. Export comprehensive reports

## Data Validation
- **New Applications**: MINOR field is required
- **Work Status**: Water Course Name is required
- **Contact Numbers**: Should be 10-digit numbers
- **Physical Progress**: Must be between 0-100%
- **Financial Status**: Must be positive numbers

## Security
- Role-based access control
- Session management
- Data isolation between subdivisions
- Secure authentication

## Support
For technical support or questions, contact:
**Executive Engineer**: DHARAM PAL
**Email**: <EMAIL>

---
© 2024 MICAD Division Hisar. All rights reserved.
