import newApplicationsData from '../data/newApplications.json';
import workStatusData from '../data/workStatus.json';
import usersData from '../data/users.json';
import subdivisionsData from '../data/subdivisions.json';
import { getCurrentUser, hasSubdivisionAccess } from './auth.js';

// In-memory storage for demo purposes
// In production, this would be replaced with actual API calls
let applications = [...newApplicationsData.applications];
let works = [...workStatusData.works];
let users = [...usersData.users];

// New Applications CRUD Operations
export const getNewApplications = (subdivision = null) => {
  const user = getCurrentUser();
  if (!user) return [];
  
  let filteredApps = applications;
  
  if (user.role === 'subdivision') {
    filteredApps = applications.filter(app => app.subdivision === user.subdivision);
  } else if (subdivision) {
    filteredApps = applications.filter(app => app.subdivision === subdivision);
  }
  
  return filteredApps.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
};

export const addNewApplication = (applicationData) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const newApp = {
    ...applicationData,
    id: `app_${Date.now()}`,
    subdivision: user.role === 'subdivision' ? user.subdivision : applicationData.subdivision,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  applications.push(newApp);
  return newApp;
};

export const updateNewApplication = (id, updates) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const appIndex = applications.findIndex(app => app.id === id);
  if (appIndex === -1) throw new Error('Application not found');
  
  const app = applications[appIndex];
  if (!hasSubdivisionAccess(app.subdivision)) {
    throw new Error('Access denied');
  }
  
  applications[appIndex] = {
    ...app,
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  return applications[appIndex];
};

export const deleteNewApplication = (id) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const appIndex = applications.findIndex(app => app.id === id);
  if (appIndex === -1) throw new Error('Application not found');
  
  const app = applications[appIndex];
  if (!hasSubdivisionAccess(app.subdivision)) {
    throw new Error('Access denied');
  }
  
  applications.splice(appIndex, 1);
  return true;
};

// Work Status CRUD Operations
export const getWorkStatus = (subdivision = null) => {
  const user = getCurrentUser();
  if (!user) return [];
  
  let filteredWorks = works;
  
  if (user.role === 'subdivision') {
    filteredWorks = works.filter(work => work.subdivision === user.subdivision);
  } else if (subdivision) {
    filteredWorks = works.filter(work => work.subdivision === subdivision);
  }
  
  return filteredWorks.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
};

export const addWorkStatus = (workData) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const newWork = {
    ...workData,
    id: `work_${Date.now()}`,
    subdivision: user.role === 'subdivision' ? user.subdivision : workData.subdivision,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  works.push(newWork);
  return newWork;
};

export const updateWorkStatus = (id, updates) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const workIndex = works.findIndex(work => work.id === id);
  if (workIndex === -1) throw new Error('Work not found');
  
  const work = works[workIndex];
  if (!hasSubdivisionAccess(work.subdivision)) {
    throw new Error('Access denied');
  }
  
  works[workIndex] = {
    ...work,
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  return works[workIndex];
};

export const deleteWorkStatus = (id) => {
  const user = getCurrentUser();
  if (!user) throw new Error('User not authenticated');
  
  const workIndex = works.findIndex(work => work.id === id);
  if (workIndex === -1) throw new Error('Work not found');
  
  const work = works[workIndex];
  if (!hasSubdivisionAccess(work.subdivision)) {
    throw new Error('Access denied');
  }
  
  works.splice(workIndex, 1);
  return true;
};

// Search functionality
export const searchData = (query, type = 'all') => {
  const user = getCurrentUser();
  if (!user) return { applications: [], works: [] };
  
  const searchTerm = query.toLowerCase();
  let searchResults = { applications: [], works: [] };
  
  if (type === 'all' || type === 'applications') {
    const appsToSearch = getNewApplications();
    searchResults.applications = appsToSearch.filter(app => 
      app.minor?.toLowerCase().includes(searchTerm) ||
      app.farmerName?.toLowerCase().includes(searchTerm) ||
      app.beneficiaryVillage?.toLowerCase().includes(searchTerm) ||
      app.status?.toLowerCase().includes(searchTerm) ||
      app.reference?.toLowerCase().includes(searchTerm)
    );
  }
  
  if (type === 'all' || type === 'works') {
    const worksToSearch = getWorkStatus();
    searchResults.works = worksToSearch.filter(work => 
      work.waterCourseName?.toLowerCase().includes(searchTerm) ||
      work.nameOfWork?.toLowerCase().includes(searchTerm) ||
      work.contractorName?.toLowerCase().includes(searchTerm) ||
      work.aaNoDate?.toLowerCase().includes(searchTerm)
    );
  }
  
  return searchResults;
};

// Dashboard statistics
export const getDashboardStats = () => {
  const user = getCurrentUser();
  if (!user) return null;
  
  const allApps = getNewApplications();
  const allWorks = getWorkStatus();
  
  return {
    totalApplications: allApps.length,
    pendingApplications: allApps.filter(app => app.status === 'Pending').length,
    inProgressApplications: allApps.filter(app => app.status === 'In Progress').length,
    completedApplications: allApps.filter(app => app.status === 'Completed').length,
    totalWorks: allWorks.length,
    ongoingWorks: allWorks.filter(work => work.currentPhysicalStatus < 100).length,
    completedWorks: allWorks.filter(work => work.currentPhysicalStatus === 100).length,
    averageProgress: allWorks.length > 0 
      ? Math.round(allWorks.reduce((sum, work) => sum + work.currentPhysicalStatus, 0) / allWorks.length)
      : 0
  };
};

// User management (Xen only)
export const getUsers = () => {
  const user = getCurrentUser();
  if (!user || user.role !== 'xen') throw new Error('Access denied');
  
  return users;
};

export const addUser = (userData) => {
  const user = getCurrentUser();
  if (!user || user.role !== 'xen') throw new Error('Access denied');
  
  const newUser = {
    ...userData,
    id: `user_${Date.now()}`
  };
  
  users.push(newUser);
  return newUser;
};

export const updateUser = (id, updates) => {
  const user = getCurrentUser();
  if (!user || user.role !== 'xen') throw new Error('Access denied');
  
  const userIndex = users.findIndex(u => u.id === id);
  if (userIndex === -1) throw new Error('User not found');
  
  users[userIndex] = { ...users[userIndex], ...updates };
  return users[userIndex];
};

export const deleteUser = (id) => {
  const user = getCurrentUser();
  if (!user || user.role !== 'xen') throw new Error('Access denied');
  
  const userIndex = users.findIndex(u => u.id === id);
  if (userIndex === -1) throw new Error('User not found');
  
  users.splice(userIndex, 1);
  return true;
};

export const getSubdivisions = () => {
  return subdivisionsData.subdivisions;
};
