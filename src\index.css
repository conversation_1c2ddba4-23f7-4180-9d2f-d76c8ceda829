@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-sans antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen;
  }

  * {
    @apply scroll-smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 border-0;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-0.5 transition-all duration-200;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300;
  }

  .card-header {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-2xl;
  }

  .input-field {
    @apply block w-full rounded-xl border border-gray-200 px-4 py-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 bg-white/80 backdrop-blur-sm transition-all duration-200;
  }

  .table-header {
    @apply bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 font-semibold text-sm uppercase tracking-wider border-b border-gray-200;
  }

  .status-pending {
    @apply bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm border border-amber-200;
  }

  .status-completed {
    @apply bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm border border-emerald-200;
  }

  .status-in-progress {
    @apply bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm border border-blue-200;
  }

  .status-rejected {
    @apply bg-gradient-to-r from-red-100 to-rose-100 text-red-800 px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm border border-red-200;
  }

  .nav-link {
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group;
  }

  .nav-link-active {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg;
  }

  .nav-link-inactive {
    @apply text-gray-600 hover:bg-white/50 hover:text-gray-900 hover:shadow-md;
  }

  .stat-card {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/50;
  }

  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2.5 overflow-hidden;
  }

  .progress-fill {
    @apply h-full rounded-full transition-all duration-500 ease-out;
  }

  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  }

  .modal-content {
    @apply bg-white rounded-2xl shadow-2xl border border-white/20 max-w-4xl w-full max-h-[90vh] overflow-y-auto;
  }

  .table-row {
    @apply hover:bg-blue-50/50 transition-colors duration-150;
  }

  .icon-button {
    @apply p-2 rounded-lg hover:bg-gray-100 transition-colors duration-150;
  }

  .search-input {
    @apply pl-12 pr-4 py-3 w-full rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 bg-white/80 backdrop-blur-sm transition-all duration-200;
  }
}