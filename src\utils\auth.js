import { LOCAL_STORAGE_KEYS } from './constants.js';
import usersData from '../data/users.json';

export const login = (username, password) => {
  const user = usersData.users.find(
    u => u.username === username && u.password === password
  );
  
  if (user) {
    const token = generateToken(user);
    localStorage.setItem(LOCAL_STORAGE_KEYS.USER, JSON.stringify(user));
    localStorage.setItem(LOCAL_STORAGE_KEYS.TOKEN, token);
    return { success: true, user, token };
  }
  
  return { success: false, message: 'Invalid credentials' };
};

export const logout = () => {
  localStorage.removeItem(LOCAL_STORAGE_KEYS.USER);
  localStorage.removeItem(LOCAL_STORAGE_KEYS.TOKEN);
};

export const getCurrentUser = () => {
  const userStr = localStorage.getItem(LOCAL_STORAGE_KEYS.USER);
  return userStr ? JSON.parse(userStr) : null;
};

export const isAuthenticated = () => {
  const token = localStorage.getItem(LOCAL_STORAGE_KEYS.TOKEN);
  const user = getCurrentUser();
  return !!(token && user);
};

export const hasXenAccess = () => {
  const user = getCurrentUser();
  return user && user.role === 'xen';
};

export const hasSubdivisionAccess = (subdivision) => {
  const user = getCurrentUser();
  if (!user) return false;
  
  if (user.role === 'xen') return true;
  if (user.role === 'subdivision') {
    return user.subdivision === subdivision;
  }
  
  return false;
};

export const getUserSubdivision = () => {
  const user = getCurrentUser();
  return user ? user.subdivision : null;
};

const generateToken = (user) => {
  // Simple token generation for demo purposes
  // In production, use proper JWT or similar
  const timestamp = Date.now();
  const payload = btoa(JSON.stringify({ userId: user.id, timestamp }));
  return payload;
};

export const validateToken = (token) => {
  try {
    const payload = JSON.parse(atob(token));
    const now = Date.now();
    const tokenAge = now - payload.timestamp;
    
    // Token expires after 24 hours
    return tokenAge < 24 * 60 * 60 * 1000;
  } catch (error) {
    return false;
  }
};
