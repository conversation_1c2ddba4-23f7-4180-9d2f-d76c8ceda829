import React, { useState, useEffect } from 'react';
import {
  Plus,
  Download,
  Upload,
  Edit,
  Trash2,
  Search,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import {
  getNewApplications,
  addNewApplication,
  updateNewApplication,
  deleteNewApplication
} from '../utils/dataManager.js';
import { exportToExcel, generateSampleExcel, parseExcelFile, validateExcelData } from '../utils/excelUtils.js';
import { APPLICATION_STATUS, WORK_TYPES, SAMPLE_NEW_APPLICATION } from '../utils/constants.js';
import { getCurrentUser } from '../utils/auth.js';

const NewApplications = () => {
  const [applications, setApplications] = useState([]);
  const [filteredApplications, setFilteredApplications] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingApp, setEditingApp] = useState(null);
  const [formData, setFormData] = useState(SAMPLE_NEW_APPLICATION);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const user = getCurrentUser();

  useEffect(() => {
    loadApplications();
  }, []);

  useEffect(() => {
    filterApplications();
  }, [applications, searchQuery, statusFilter]);

  const loadApplications = () => {
    setLoading(true);
    try {
      const apps = getNewApplications();
      setApplications(apps);
    } catch (error) {
      showMessage('error', 'Error loading applications: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const filterApplications = () => {
    let filtered = applications;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(app =>
        app.minor?.toLowerCase().includes(query) ||
        app.farmerName?.toLowerCase().includes(query) ||
        app.beneficiaryVillage?.toLowerCase().includes(query) ||
        app.reference?.toLowerCase().includes(query)
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    setFilteredApplications(filtered);
  };

  const showMessage = (type, text) => {
    setMessage({ type, text });
    setTimeout(() => setMessage({ type: '', text: '' }), 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.minor.trim()) {
      showMessage('error', 'MINOR (water course name) is required');
      return;
    }

    try {
      if (editingApp) {
        await updateNewApplication(editingApp.id, formData);
        showMessage('success', 'Application updated successfully');
      } else {
        await addNewApplication(formData);
        showMessage('success', 'Application added successfully');
      }

      setShowForm(false);
      setEditingApp(null);
      setFormData(SAMPLE_NEW_APPLICATION);
      loadApplications();
    } catch (error) {
      showMessage('error', error.message);
    }
  };

  const handleEdit = (app) => {
    setEditingApp(app);
    setFormData({ ...app });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this application?')) {
      try {
        await deleteNewApplication(id);
        showMessage('success', 'Application deleted successfully');
        loadApplications();
      } catch (error) {
        showMessage('error', error.message);
      }
    }
  };

  const handleExport = () => {
    const dataToExport = filteredApplications.length > 0 ? filteredApplications : applications;
    exportToExcel(dataToExport, 'applications', 'new_applications.xlsx');
    showMessage('success', 'Data exported successfully');
  };

  const handleSampleDownload = () => {
    generateSampleExcel('applications');
    showMessage('success', 'Sample file downloaded');
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setUploading(true);
    try {
      const result = await parseExcelFile(file, 'applications');
      const errors = validateExcelData(result.data, 'applications');

      if (errors.length > 0) {
        showMessage('error', `Validation errors: ${errors.join(', ')}`);
        return;
      }

      // Add all valid applications
      for (const appData of result.data) {
        await addNewApplication(appData);
      }

      showMessage('success', `Successfully imported ${result.validRows} applications`);
      loadApplications();
    } catch (error) {
      showMessage('error', 'Error importing file: ' + error.message);
    } finally {
      setUploading(false);
      e.target.value = '';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">New Applications</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage farmer applications received at subdivision level
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowForm(true)}
            className="btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Application
          </button>
        </div>
      </div>

      {/* Message */}
      {message.text && (
        <div className={`p-4 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
            'bg-red-50 text-red-800 border border-red-200'
          }`}>
          <div className="flex">
            {message.type === 'success' ?
              <CheckCircle className="h-5 w-5 mr-2" /> :
              <AlertCircle className="h-5 w-5 mr-2" />
            }
            <span>{message.text}</span>
          </div>
        </div>
      )}

      {/* Filters and Actions */}
      <div className="card p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field pl-10 w-full sm:w-64"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field w-full sm:w-auto"
            >
              <option value="">All Status</option>
              {APPLICATION_STATUS.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <button
              onClick={handleSampleDownload}
              className="btn-secondary"
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Sample Excel
            </button>
            <label className="btn-secondary cursor-pointer">
              <Upload className="h-4 w-4 mr-2" />
              {uploading ? 'Uploading...' : 'Import Excel'}
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileUpload}
                className="hidden"
                disabled={uploading}
              />
            </label>
            <button
              onClick={handleExport}
              className="btn-secondary"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </button>
          </div>
        </div>
      </div>

      {/* Applications Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="table-header">
              <tr>
                <th className="px-6 py-3 text-left">Sr. No</th>
                <th className="px-6 py-3 text-left">RD</th>
                <th className="px-6 py-3 text-left">Minor</th>
                <th className="px-6 py-3 text-left">Village</th>
                <th className="px-6 py-3 text-left">Farmer Name</th>
                <th className="px-6 py-3 text-left">Status</th>
                <th className="px-6 py-3 text-left">Type</th>
                <th className="px-6 py-3 text-left">Contact</th>
                <th className="px-6 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredApplications.map((app) => (
                <tr key={app.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.srNo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.rd}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {app.minor}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.beneficiaryVillage}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.farmerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`status-${app.status?.toLowerCase().replace(' ', '-')}`}>
                      {app.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.extensionRemodelingKachcha}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {app.contactNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(app)}
                        className="text-primary-600 hover:text-primary-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(app.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredApplications.length === 0 && (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No applications found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchQuery || statusFilter ? 'Try adjusting your filters' : 'Get started by adding a new application'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingApp ? 'Edit Application' : 'Add New Application'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Sr. No</label>
                    <input
                      type="text"
                      value={formData.srNo}
                      onChange={(e) => setFormData({ ...formData, srNo: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">RD</label>
                    <input
                      type="text"
                      value={formData.rd}
                      onChange={(e) => setFormData({ ...formData, rd: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">
                      MINOR (Water Course Name) <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.minor}
                      onChange={(e) => setFormData({ ...formData, minor: e.target.value })}
                      className="input-field"
                      placeholder="Enter water course name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Beneficiary Village</label>
                    <input
                      type="text"
                      value={formData.beneficiaryVillage}
                      onChange={(e) => setFormData({ ...formData, beneficiaryVillage: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                      className="input-field"
                    >
                      {APPLICATION_STATUS.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Type</label>
                    <select
                      value={formData.extensionRemodelingKachcha}
                      onChange={(e) => setFormData({ ...formData, extensionRemodelingKachcha: e.target.value })}
                      className="input-field"
                    >
                      <option value="">Select Type</option>
                      {WORK_TYPES.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Farmer's Name</label>
                    <input
                      type="text"
                      value={formData.farmerName}
                      onChange={(e) => setFormData({ ...formData, farmerName: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Contact Number</label>
                    <input
                      type="tel"
                      value={formData.contactNumber}
                      onChange={(e) => setFormData({ ...formData, contactNumber: e.target.value })}
                      className="input-field"
                      placeholder="10-digit mobile number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Reference</label>
                    <input
                      type="text"
                      value={formData.reference}
                      onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                      className="input-field"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">ATR (Action Taken Report)</label>
                    <textarea
                      value={formData.atr}
                      onChange={(e) => setFormData({ ...formData, atr: e.target.value })}
                      className="input-field"
                      rows="3"
                      placeholder="Add remarks or action taken report..."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingApp(null);
                      setFormData(SAMPLE_NEW_APPLICATION);
                    }}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    {editingApp ? 'Update' : 'Add'} Application
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewApplications;
